using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// النموذج الرئيسي للتطبيق
    /// Main application form
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration;
        private readonly ILogger<MainForm> _logger;
        private readonly IAuthenticationService _authenticationService;
        private readonly IAuthorizationService _authorizationService;

        // Dashboard card labels for updating values
        private Label? _activePondsLabel;
        private Label? _activeCyclesLabel;
        private Label? _totalEmployeesLabel;
        private Label? _todayTransactionsLabel;

        public MainForm(IServiceProvider serviceProvider, IConfiguration configuration, ILogger<MainForm> logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _authenticationService = _serviceProvider.GetRequiredService<IAuthenticationService>();
            _authorizationService = _serviceProvider.GetRequiredService<IAuthorizationService>();

            InitializeForm();
            InitializeApplication();

            // Subscribe to Load event for async initialization
            Load += MainForm_Load;
        }

        private void InitializeForm()
        {
            InitializeComponent();

            // Form properties
            this.Text = _configuration["Application:Name"] ?? "نظام إدارة مزرعة الأسماك";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Create menu strip
            CreateMenuStrip();

            // Create toolbar
            CreateToolStrip();

            // Create status bar
            CreateStatusStrip();

            // Create main panel
            CreateMainPanel();
        }

        private void CreateMenuStrip()
        {
            var menuStrip = new MenuStrip
            {
                RightToLeft = RightToLeft.Yes,
                Font = new Font("Segoe UI", 10F)
            };

            // File menu
            var fileMenu = new ToolStripMenuItem("ملف") { Name = "fileMenu" };
            fileMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("جديد", null, (s, e) => NewFile()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("نسخ احتياطي", null, (s, e) => CreateBackup()),
                new ToolStripMenuItem("استعادة", null, (s, e) => RestoreBackup()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("خروج", null, (s, e) => this.Close())
            });

            // Management menu
            var managementMenu = new ToolStripMenuItem("الإدارة") { Name = "managementMenu" };
            managementMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إدارة الأحواض", null, (s, e) => OpenPondManagement()) { Name = "pondManagementToolStripMenuItem" },
                new ToolStripMenuItem("الدورات الإنتاجية", null, (s, e) => OpenProductionCycles()) { Name = "productionCyclesToolStripMenuItem" },
                new ToolStripMenuItem("إدارة الموظفين", null, (s, e) => OpenEmployeeManagement()) { Name = "employeeManagementToolStripMenuItem" },
                new ToolStripMenuItem("المخزون", null, (s, e) => OpenInventoryManagement()) { Name = "inventoryToolStripMenuItem" }
            });

            // Accounting menu
            var accountingMenu = new ToolStripMenuItem("المحاسبة") { Name = "accountingMenu" };
            accountingMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("القيود المحاسبية", null, (s, e) => OpenAccounting()) { Name = "accountingEntriesToolStripMenuItem" },
                new ToolStripMenuItem("دليل الحسابات", null, (s, e) => OpenChartOfAccounts()) { Name = "chartOfAccountsToolStripMenuItem" },
                new ToolStripMenuItem("مراكز التكلفة", null, (s, e) => OpenCostCenters()) { Name = "costCentersToolStripMenuItem" }
            });

            // Reports menu
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("تقارير الإنتاج", null, (s, e) => OpenProductionReports()),
                new ToolStripMenuItem("التقارير المالية", null, (s, e) => OpenFinancialReports()),
                new ToolStripMenuItem("تقارير الموظفين", null, (s, e) => OpenEmployeeReports())
            });

            // Tools menu
            var toolsMenu = new ToolStripMenuItem("الأدوات") { Name = "toolsMenu" };
            toolsMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("الإعدادات", null, (s, e) => OpenSettings()),
                new ToolStripMenuItem("معلومات المزرعة", null, (s, e) => OpenFarmInfo()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تحسين قاعدة البيانات", null, (s, e) => OptimizeDatabase())
            });

            // Help menu
            var helpMenu = new ToolStripMenuItem("المساعدة") { Name = "helpMenu" };
            helpMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("فحص التحديثات", null, (s, e) => CheckForUpdates()),
                new ToolStripMenuItem("مراقب الأداء", null, (s, e) => ShowPerformanceMonitor()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("دليل المستخدم", null, (s, e) => ShowUserGuide()),
                new ToolStripMenuItem("حول البرنامج", null, (s, e) => ShowAbout())
            });

            menuStrip.Items.AddRange(new ToolStripItem[]
            {
                fileMenu, managementMenu, accountingMenu, reportsMenu, toolsMenu, helpMenu
            });

            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateToolStrip()
        {
            var toolStrip = new ToolStrip
            {
                ImageScalingSize = new Size(32, 32),
                Font = new Font("Segoe UI", 9F)
            };

            toolStrip.Items.AddRange(new ToolStripItem[]
            {
                new ToolStripButton("الأحواض", null, (s, e) => OpenPondManagement()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText },
                new ToolStripButton("الدورات", null, (s, e) => OpenProductionCycles()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText },
                new ToolStripButton("الموظفين", null, (s, e) => OpenEmployeeManagement()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText },
                new ToolStripSeparator(),
                new ToolStripButton("المحاسبة", null, (s, e) => OpenAccounting()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText },
                new ToolStripButton("التقارير", null, (s, e) => OpenReports()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText },
                new ToolStripSeparator(),
                new ToolStripButton("نسخ احتياطي", null, (s, e) => CreateBackup()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText }
            });

            this.Controls.Add(toolStrip);
        }

        private void CreateStatusStrip()
        {
            var statusStrip = new StatusStrip();
            
            var statusLabel = new ToolStripStatusLabel("جاهز")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var dateLabel = new ToolStripStatusLabel(DateTime.Now.ToString("yyyy/MM/dd"))
            {
                TextAlign = ContentAlignment.MiddleRight
            };

            var timeLabel = new ToolStripStatusLabel(DateTime.Now.ToString("HH:mm"))
            {
                TextAlign = ContentAlignment.MiddleRight
            };

            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, dateLabel, timeLabel });
            this.Controls.Add(statusStrip);

            // Update time every second
            var timer = new System.Windows.Forms.Timer { Interval = 1000 };
            timer.Tick += (s, e) =>
            {
                timeLabel.Text = DateTime.Now.ToString("HH:mm");
                dateLabel.Text = DateTime.Now.ToString("yyyy/MM/dd");
            };
            timer.Start();
        }

        private void CreateMainPanel()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            // Create dashboard
            CreateDashboard(mainPanel);

            this.Controls.Add(mainPanel);
        }

        private void CreateDashboard(Panel parent)
        {
            var dashboardPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 2,
                Padding = new Padding(20)
            };

            // Set column and row styles
            dashboardPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            dashboardPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            dashboardPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            dashboardPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            dashboardPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));

            // Create dashboard cards
            var cards = new[]
            {
                CreateDashboardCard("الأحواض النشطة", "0", Color.FromArgb(52, 152, 219), () => OpenPondManagement(), out _activePondsLabel),
                CreateDashboardCard("الدورات الجارية", "0", Color.FromArgb(46, 204, 113), () => OpenProductionCycles(), out _activeCyclesLabel),
                CreateDashboardCard("إجمالي الموظفين", "0", Color.FromArgb(155, 89, 182), () => OpenEmployeeManagement(), out _totalEmployeesLabel),
                CreateDashboardCard("المعاملات اليوم", "0", Color.FromArgb(230, 126, 34), () => OpenAccounting(), out _todayTransactionsLabel),
                CreateDashboardCard("التقارير", "عرض", Color.FromArgb(231, 76, 60), () => OpenReports(), out _),
                CreateDashboardCard("الإعدادات", "تكوين", Color.FromArgb(52, 73, 94), () => OpenSettings(), out _)
            };

            for (int i = 0; i < cards.Length; i++)
            {
                dashboardPanel.Controls.Add(cards[i], i % 3, i / 3);
            }

            parent.Controls.Add(dashboardPanel);
        }

        private Panel CreateDashboardCard(string title, string value, Color color, Action clickAction, out Label valueLabel)
        {
            var card = new Panel
            {
                BackColor = Color.White,
                Margin = new Padding(10),
                Cursor = Cursors.Hand
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = color,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40
            };

            valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 24F, FontStyle.Bold),
                ForeColor = color,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            card.Controls.Add(valueLabel);
            card.Controls.Add(titleLabel);

            card.Click += (s, e) => clickAction?.Invoke();
            titleLabel.Click += (s, e) => clickAction?.Invoke();
            valueLabel.Click += (s, e) => clickAction?.Invoke();

            // Add hover effect
            card.MouseEnter += (s, e) => card.BackColor = Color.FromArgb(248, 248, 248);
            card.MouseLeave += (s, e) => card.BackColor = Color.White;

            return card;
        }

        private void InitializeApplication()
        {
            try
            {
                _logger.LogInformation("تم بدء تشغيل التطبيق بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة التطبيق");
                MessageBox.Show($"خطأ في تهيئة التطبيق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void MainForm_Load(object? sender, EventArgs e)
        {
            // Initialize default data first
            try
            {
                var userManagementService = _serviceProvider.GetRequiredService<IUserManagementService>();
                await userManagementService.InitializeDefaultDataAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة البيانات الافتراضية");
            }

            await LoadDashboardDataAsync();
            await ApplyUserPermissionsAsync();
            ShowUserInfo();
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                var statisticsService = _serviceProvider.GetRequiredService<IStatisticsService>();
                var stats = await statisticsService.GetDashboardStatisticsAsync();

                // Update dashboard cards with actual data
                if (_activePondsLabel != null)
                    _activePondsLabel.Text = stats.ActivePonds.ToString();

                if (_activeCyclesLabel != null)
                    _activeCyclesLabel.Text = stats.ActiveCycles.ToString();

                if (_totalEmployeesLabel != null)
                    _totalEmployeesLabel.Text = stats.TotalEmployees.ToString();

                if (_todayTransactionsLabel != null)
                    _todayTransactionsLabel.Text = stats.TodayTransactions.ToString();

                _logger.LogInformation("تم تحميل بيانات لوحة المعلومات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات لوحة المعلومات");
                MessageBox.Show("حدث خطأ أثناء تحميل بيانات لوحة المعلومات.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Event handlers for menu items
        private void NewFile() => MessageBox.Show("إنشاء ملف جديد", "معلومات");

        private void CreateBackup()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedBackupForm>();
            form.ShowDialog();
        }

        private void RestoreBackup()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedBackupForm>();
            form.ShowDialog();
        }
        
        private void OpenPondManagement()
        {
            var form = _serviceProvider.GetRequiredService<PondManagementForm>();
            form.ShowDialog();
        }
        
        private void OpenProductionCycles()
        {
            var form = _serviceProvider.GetRequiredService<ProductionCycleForm>();
            form.ShowDialog();
        }
        
        private void OpenEmployeeManagement()
        {
            var form = _serviceProvider.GetRequiredService<EmployeeManagementForm>();
            form.ShowDialog();
        }
        
        private void OpenInventoryManagement()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedInventoryManagementForm>();
            form.ShowDialog();
        }
        
        private void OpenAccounting()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedAccountingForm>();
            form.ShowDialog();
        }

        private void OpenDashboard()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedDashboardForm>();
            form.ShowDialog();
        }
        
        private void OpenChartOfAccounts()
        {
            var form = _serviceProvider.GetRequiredService<ChartOfAccountsForm>();
            form.ShowDialog();
        }

        private void OpenCostCenters()
        {
            var form = _serviceProvider.GetRequiredService<CostCentersForm>();
            form.ShowDialog();
        }
        private void OpenProductionReports()
        {
            var form = _serviceProvider.GetRequiredService<ProductionReportsForm>();
            form.ShowDialog();
        }

        private void OpenFinancialReports()
        {
            var form = _serviceProvider.GetRequiredService<FinancialReportsForm>();
            form.ShowDialog();
        }

        private void OpenEmployeeReports()
        {
            var form = _serviceProvider.GetRequiredService<EmployeeReportsForm>();
            form.ShowDialog();
        }
        
        private void OpenReports()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedReportsForm>();
            form.ShowDialog();
        }
        
        private void OpenSettings()
        {
            var form = _serviceProvider.GetRequiredService<SettingsForm>();
            form.ShowDialog();
        }
        
        private void OpenFarmInfo()
        {
            var form = _serviceProvider.GetRequiredService<FarmInfoForm>();
            form.ShowDialog();
        }

        private void OptimizeDatabase()
        {
            var form = _serviceProvider.GetRequiredService<DatabaseOptimizationForm>();
            form.ShowDialog();
        }

        private void ShowUserGuide()
        {
            var form = _serviceProvider.GetRequiredService<UserGuideForm>();
            form.ShowDialog();
        }
        
        private void ShowAbout()
        {
            var aboutText = $@"
{_configuration["Application:Name"]}
الإصدار: {_configuration["Application:Version"]}

نظام متكامل لإدارة ومحاسبة مزارع الأسماك

المطور: {_configuration["AssemblyCompany"]}
البريد الإلكتروني: <EMAIL>

© 2024 جميع الحقوق محفوظة
";
            MessageBox.Show(aboutText, "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async Task ApplyUserPermissionsAsync()
        {
            try
            {
                var currentUser = _authenticationService.GetCurrentUser();
                if (currentUser == null)
                {
                    _logger.LogWarning("لا يوجد مستخدم مسجل دخول");
                    return;
                }

                // Check permissions for menu items
                var menuStrip = this.Controls.OfType<MenuStrip>().FirstOrDefault();
                if (menuStrip != null)
                {
                    await ApplyMenuPermissionsAsync(menuStrip);
                }

                _logger.LogInformation("تم تطبيق الصلاحيات للمستخدم: {Username}", currentUser.Username);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تطبيق الصلاحيات");
            }
        }

        private async Task ApplyMenuPermissionsAsync(MenuStrip menuStrip)
        {
            foreach (ToolStripMenuItem menuItem in menuStrip.Items.OfType<ToolStripMenuItem>())
            {
                // The switch statement now works because the Name property is set.
                switch (menuItem.Name) 
                {
                    case "managementMenu":
                        menuItem.Visible = true;
                        break;
                    case "accountingToolStripMenuItem":
                        menuItem.Visible = true;
                        break;
                    case "reportsMenu":
                        menuItem.Visible = true;
                        break;
                    case "toolsMenu":
                        // Example: Only admins can see the tools menu
                        menuItem.Visible = await _authorizationService.IsSystemAdminAsync();
                        break;
                }

                // Apply permissions to sub-menu items
                ApplySubMenuPermissions(menuItem);
            }
        }

        private static void ApplySubMenuPermissions(ToolStripMenuItem parentMenuItem)
        {
            foreach (ToolStripMenuItem subMenuItem in parentMenuItem.DropDownItems.OfType<ToolStripMenuItem>())
            {
                // Apply specific permissions based on sub-menu item names
                switch (subMenuItem.Name)
                {
                    case "userManagementToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "roleManagementToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "systemSettingsToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "databaseManagementToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "payrollManagementToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "financialReportsToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "productionReportsToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "employeeReportsToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "inventoryReportsToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                }
            }
        }

        private void ShowUserInfo()
        {
            try
            {
                var currentUser = _authenticationService.GetCurrentUser();
                if (currentUser != null)
                {
                    // Update window title with user info
                    this.Text = $"نظام إدارة مزرعة الأسماك - {currentUser.FullName}";

                    // Update status bar if exists
                    var statusStrip = this.Controls.OfType<StatusStrip>().FirstOrDefault();
                    if (statusStrip != null)
                    {
                        var userLabel = statusStrip.Items.OfType<ToolStripStatusLabel>()
                            .FirstOrDefault(l => l.Name == "userStatusLabel");

                        if (userLabel == null)
                        {
                            userLabel = new ToolStripStatusLabel("userStatusLabel")
                            {
                                Name = "userStatusLabel",
                                Spring = true,
                                TextAlign = ContentAlignment.MiddleRight
                            };
                            statusStrip.Items.Add(userLabel);
                        }

                        userLabel.Text = $"المستخدم: {currentUser.FullName} | آخر دخول: {currentUser.LastLoginDate:yyyy-MM-dd HH:mm}";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في عرض معلومات المستخدم");
            }
        }

        /// <summary>
        /// فحص التحديثات
        /// Check for updates
        /// </summary>
        private void CheckForUpdates()
        {
            try
            {
                _logger.LogInformation("فتح نموذج فحص التحديثات");

                // هنا يمكن إنشاء UpdateService من خلال DI
                // للتبسيط، سنعرض رسالة
                MessageBox.Show("ميزة فحص التحديثات ستكون متاحة قريباً.", "فحص التحديثات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص التحديثات");
                MessageBox.Show("حدث خطأ أثناء فحص التحديثات.", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض مراقب الأداء
        /// Show performance monitor
        /// </summary>
        private void ShowPerformanceMonitor()
        {
            try
            {
                _logger.LogInformation("فتح مراقب الأداء");

                // هنا يمكن إنشاء PerformanceMonitorService من خلال DI
                // للتبسيط، سنعرض رسالة
                MessageBox.Show("مراقب الأداء سيكون متاحاً قريباً.", "مراقب الأداء",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح مراقب الأداء");
                MessageBox.Show("حدث خطأ أثناء فتح مراقب الأداء.", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region Event Handlers

        private void ExitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void PondsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open Ponds Management Form
        }

        private void ProductionCyclesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open Production Cycles Form
        }

        private void EmployeesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open Employees Management Form
        }

        private void InventoryToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open Inventory Management Form
        }

        private void TransactionsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open Transactions Form
        }

        private void AccountsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open Accounts Form
        }

        private void ProductionReportsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open Production Reports Form
        }

        private void FinancialReportsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open Financial Reports Form
        }

        private void BackupToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open Backup Form
        }

        private void SettingsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open Settings Form
        }

        private void UserGuideToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Open User Guide
        }

        private void AboutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // Show About Dialog
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            // Update time display
        }

        // Additional event handlers for Designer
        private void ChartOfAccountsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            OpenChartOfAccounts();
        }

        private void ReportsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            OpenReports();
        }

        private void CyclesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            OpenProductionCycles();
        }

        private void FeedingToolStripMenuItem_Click(object sender, EventArgs e)
        {
            OpenFeedingRecords();
        }

        private void MortalityToolStripMenuItem_Click(object sender, EventArgs e)
        {
            OpenMortalityRecords();
        }

        private void EmployeeManagementToolStripMenuItem_Click(object sender, EventArgs e)
        {
            OpenEmployeeManagement();
        }

        private void PayrollToolStripMenuItem_Click(object sender, EventArgs e)
        {
            OpenPayrollManagement();
        }

        private void InventoryManagementToolStripMenuItem_Click(object sender, EventArgs e)
        {
            OpenInventoryManagement();
        }

        private void BackupRestoreToolStripMenuItem_Click(object sender, EventArgs e)
        {
            CreateBackup();
        }

        // Missing methods
        private void OpenFeedingRecords()
        {
            var unitOfWork = _serviceProvider.GetRequiredService<IUnitOfWork>();
            var logger = _serviceProvider.GetRequiredService<ILogger<FeedingRecordForm>>();
            var form = new FeedingRecordForm(unitOfWork, logger);
            form.MdiParent = this;
            form.Show();
        }

        private void OpenMortalityRecords()
        {
            var unitOfWork = _serviceProvider.GetRequiredService<IUnitOfWork>();
            var logger = _serviceProvider.GetRequiredService<ILogger<MortalityRecordForm>>();
            var form = new MortalityRecordForm(unitOfWork, logger);
            form.MdiParent = this;
            form.Show();
        }

        private void OpenPayrollManagement()
        {
            var unitOfWork = _serviceProvider.GetRequiredService<IUnitOfWork>();
            var logger = _serviceProvider.GetRequiredService<ILogger<PayrollManagementForm>>();
            var employee = new Employee(); // Create empty employee for general payroll management
            var form = new PayrollManagementForm(unitOfWork, logger, employee);
            form.MdiParent = this;
            form.Show();
        }



        #endregion
    }
}
